/**
 * 🎮 星空方块大师 - 创新俄罗斯方块微信小游戏
 * 作者: Brovo865
 * 版本: 2.1.0 (修正版)
 * 创建时间: 2025-07-19 11:08:52
 * 
 * 🌟 核心创新特性:
 * - 五种游戏模式 (经典/重力/炸弹/竞速/合作)
 * - 八种特殊方块系统
 * - 技能槽与超能力系统
 * - 实时社交排行榜
 * - 动态视觉特效系统
 * - 主题切换系统
 * - 成就与分享系统
 */

// ==================== 游戏初始化 ====================
console.log('🎮 星空方块大师启动中...');

// 获取Canvas和上下文
const canvas = wx.createCanvas();
const ctx = canvas.getContext('2d');

// 获取屏幕尺寸并适配
const systemInfo = wx.getSystemInfoSync();
const { windowWidth, windowHeight, pixelRatio } = systemInfo;

// 设置Canvas尺寸
canvas.width = windowWidth * pixelRatio;
canvas.height = windowHeight * pixelRatio;
ctx.scale(pixelRatio, pixelRatio);

console.log(`📱 设备信息: ${windowWidth}x${windowHeight}, 像素比: ${pixelRatio}`);

// ==================== 游戏配置 ====================
const GAME_CONFIG = {
  // 基础配置
  GRID_WIDTH: 10,
  GRID_HEIGHT: 20,
  CELL_SIZE: Math.min(windowWidth / 12, windowHeight / 26),

  // 游戏速度
  INITIAL_SPEED: 1000,
  MIN_SPEED: 80,
  SPEED_MULTIPLIER: 0.92,

  // 特效配置
  PARTICLE_COUNT: 20,
  EXPLOSION_PARTICLES: 35,
  ANIMATION_DURATION: 400,

  // 技能配置
  SKILL_CHARGE_RATE: 2,
  MAX_SKILL_CHARGE: 100,

  // 音效配置
  AUDIO_ENABLED: true,
  MUSIC_VOLUME: 0.6,
  SFX_VOLUME: 0.8,

  // 版本信息
  VERSION: '2.2.0',
  BUILD_TIME: '2025-07-19 11:08:52'
};

// 计算游戏区域位置
const GAME_AREA = {
  x: Math.max(10, (windowWidth - GAME_CONFIG.GRID_WIDTH * GAME_CONFIG.CELL_SIZE) / 2),
  y: Math.max(80, (windowHeight - GAME_CONFIG.GRID_HEIGHT * GAME_CONFIG.CELL_SIZE) / 2),
  width: GAME_CONFIG.GRID_WIDTH * GAME_CONFIG.CELL_SIZE,
  height: GAME_CONFIG.GRID_HEIGHT * GAME_CONFIG.CELL_SIZE
};

// ==================== 音效系统 ====================
const AUDIO_MANAGER = {
  // 音频对象存储
  sounds: {},
  music: null,

  // 音效文件映射 (使用微信小游戏音频API)
  soundFiles: {
    // 基础音效
    move: 'sounds/move.mp3',
    rotate: 'sounds/rotate.mp3',
    drop: 'sounds/drop.mp3',
    land: 'sounds/land.mp3',

    // 消除音效
    clear1: 'sounds/clear1.mp3',
    clear2: 'sounds/clear2.mp3',
    clear3: 'sounds/clear3.mp3',
    clear4: 'sounds/clear4.mp3',

    // 特殊音效
    bomb: 'sounds/bomb.mp3',
    lightning: 'sounds/lightning.mp3',
    rainbow: 'sounds/rainbow.mp3',
    gold: 'sounds/gold.mp3',

    // 游戏状态音效
    levelup: 'sounds/levelup.mp3',
    gameover: 'sounds/gameover.mp3',
    pause: 'sounds/pause.mp3',
    resume: 'sounds/resume.mp3',

    // 背景音乐
    bgm_menu: 'music/menu.mp3',
    bgm_game: 'music/game.mp3',
    bgm_intense: 'music/intense.mp3'
  },

  // 初始化音效系统
  init() {
    console.log('🎵 初始化音效系统...');

    // 预加载音效文件
    Object.keys(this.soundFiles).forEach(key => {
      try {
        if (key.startsWith('bgm_')) {
          // 背景音乐使用循环播放
          this.sounds[key] = wx.createInnerAudioContext();
          this.sounds[key].src = this.soundFiles[key];
          this.sounds[key].loop = true;
          this.sounds[key].volume = GAME_CONFIG.MUSIC_VOLUME;
        } else {
          // 音效使用短音频
          this.sounds[key] = wx.createInnerAudioContext();
          this.sounds[key].src = this.soundFiles[key];
          this.sounds[key].volume = GAME_CONFIG.SFX_VOLUME;
        }
      } catch (error) {
        console.warn(`音效加载失败: ${key}`, error);
        // 创建静音占位符
        this.sounds[key] = { play: () => {}, stop: () => {}, pause: () => {} };
      }
    });

    console.log('✅ 音效系统初始化完成');
  },

  // 播放音效
  playSound(soundName, volume = 1.0) {
    if (!GAME_CONFIG.AUDIO_ENABLED || !this.sounds[soundName]) return;

    try {
      const sound = this.sounds[soundName];
      sound.volume = GAME_CONFIG.SFX_VOLUME * volume;
      sound.currentTime = 0; // 重置播放位置
      sound.play();
    } catch (error) {
      console.warn(`播放音效失败: ${soundName}`, error);
    }
  },

  // 播放背景音乐
  playMusic(musicName) {
    if (!GAME_CONFIG.AUDIO_ENABLED) return;

    // 停止当前音乐
    this.stopMusic();

    if (this.sounds[musicName]) {
      try {
        this.music = this.sounds[musicName];
        this.music.volume = GAME_CONFIG.MUSIC_VOLUME;
        this.music.play();
        console.log(`🎵 播放背景音乐: ${musicName}`);
      } catch (error) {
        console.warn(`播放背景音乐失败: ${musicName}`, error);
      }
    }
  },

  // 停止背景音乐
  stopMusic() {
    if (this.music) {
      try {
        this.music.stop();
        this.music = null;
      } catch (error) {
        console.warn('停止背景音乐失败', error);
      }
    }
  },

  // 暂停背景音乐
  pauseMusic() {
    if (this.music) {
      try {
        this.music.pause();
      } catch (error) {
        console.warn('暂停背景音乐失败', error);
      }
    }
  },

  // 恢复背景音乐
  resumeMusic() {
    if (this.music) {
      try {
        this.music.play();
      } catch (error) {
        console.warn('恢复背景音乐失败', error);
      }
    }
  },

  // 设置音效开关
  toggleAudio() {
    GAME_CONFIG.AUDIO_ENABLED = !GAME_CONFIG.AUDIO_ENABLED;
    if (!GAME_CONFIG.AUDIO_ENABLED) {
      this.stopMusic();
    }
    return GAME_CONFIG.AUDIO_ENABLED;
  },

  // 设置音量
  setVolume(musicVolume, sfxVolume) {
    GAME_CONFIG.MUSIC_VOLUME = Math.max(0, Math.min(1, musicVolume));
    GAME_CONFIG.SFX_VOLUME = Math.max(0, Math.min(1, sfxVolume));

    // 更新当前播放的音乐音量
    if (this.music) {
      this.music.volume = GAME_CONFIG.MUSIC_VOLUME;
    }
  }
};

// ==================== 方块定义系统 ====================
const TETROMINOS = {
  // 经典方块
  I: {
    shape: [
      [0, 0, 0, 0],
      [1, 1, 1, 1],
      [0, 0, 0, 0],
      [0, 0, 0, 0]
    ],
    color: '#00FFFF',
    type: 'normal'
  },
  O: {
    shape: [
      [1, 1],
      [1, 1]
    ],
    color: '#FFFF00',
    type: 'normal'
  },
  T: {
    shape: [
      [0, 1, 0],
      [1, 1, 1],
      [0, 0, 0]
    ],
    color: '#800080',
    type: 'normal'
  },
  S: {
    shape: [
      [0, 1, 1],
      [1, 1, 0],
      [0, 0, 0]
    ],
    color: '#00FF00',
    type: 'normal'
  },
  Z: {
    shape: [
      [1, 1, 0],
      [0, 1, 1],
      [0, 0, 0]
    ],
    color: '#FF0000',
    type: 'normal'
  },
  J: {
    shape: [
      [1, 0, 0],
      [1, 1, 1],
      [0, 0, 0]
    ],
    color: '#0000FF',
    type: 'normal'
  },
  L: {
    shape: [
      [0, 0, 1],
      [1, 1, 1],
      [0, 0, 0]
    ],
    color: '#FFA500',
    type: 'normal'
  },
  
  // 特殊方块
  BOMB: {
    shape: [[1]],
    color: '#FF4444',
    type: 'bomb',
    icon: '💣'
  },
  LIGHTNING: {
    shape: [[1]],
    color: '#FFFF44',
    type: 'lightning',
    icon: '⚡'
  },
  RAINBOW: {
    shape: [[1]],
    color: '#FF69B4',
    type: 'rainbow',
    icon: '🌈'
  },
  GOLD: {
    shape: [[1]],
    color: '#FFD700',
    type: 'gold',
    icon: '⭐',
    multiplier: 2
  }
};

// ==================== 游戏状态管理 ====================
let gameState = {
  // 基础状态
  isRunning: false,
  isPaused: false,
  gameMode: 'classic',
  currentScreen: 'menu',

  // 分数与等级
  score: 0,
  level: 1,
  lines: 0,
  combo: 0,
  maxCombo: 0,

  // 计时
  gameTime: 0,
  lastUpdateTime: 0,
  fallTime: 0,
  frameCount: 0,

  // 技能系统
  skillCharge: 0,
  activeSkills: [],
  skillCooldowns: {},

  // 特效
  particles: [],
  animations: [],
  screenShake: 0,
  flashEffect: 0,

  // 统计
  piecesPlaced: 0,
  specialBlocksUsed: 0,

  // 用户数据
  playerName: 'Brovo865',
  highScore: 0,
  achievements: [],

  // 网格
  grid: [],

  // 方块
  currentPiece: null,
  nextPieces: [],
  holdPiece: null,
  canHold: true,
  ghostPiece: null,

  // 模式特殊状态
  gravityDirection: 'down',
  bombsRemaining: 5,
  timeLimit: 180000,
  timeRemaining: 180000,

  // 音效状态
  currentBGM: null,
  lastSoundTime: 0,
  soundCooldown: 50 // 防止音效重叠的冷却时间(ms)
};

// ==================== 粒子系统 ====================
class Particle {
  constructor(x, y, color, type = 'normal') {
    this.x = x;
    this.y = y;
    this.vx = (Math.random() - 0.5) * 8;
    this.vy = (Math.random() - 0.5) * 8;
    this.life = 1.0;
    this.size = Math.random() * 4 + 2;
    this.color = color;
    this.type = type;
    
    switch (type) {
      case 'explosion':
        this.vx *= 2;
        this.vy *= 2;
        this.size *= 1.5;
        this.decay = 0.04;
        break;
      case 'spark':
        this.vy -= Math.random() * 4;
        this.decay = 0.03;
        break;
      default:
        this.decay = 0.025;
    }
  }

  update() {
    this.x += this.vx;
    this.y += this.vy;
    this.vx *= 0.98;
    this.vy *= 0.98;
    
    if (this.type === 'spark') {
      this.vy += 0.2;
    }
    
    this.life -= this.decay;
    this.size *= 0.99;
  }

  draw() {
    if (this.life <= 0) return;
    
    ctx.save();
    ctx.globalAlpha = this.life;
    
    if (this.type === 'explosion') {
      ctx.shadowColor = this.color;
      ctx.shadowBlur = 10;
    }
    
    ctx.fillStyle = this.color;
    ctx.beginPath();
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
    ctx.fill();
    
    ctx.restore();
  }

  isDead() {
    return this.life <= 0 || this.size <= 0.1;
  }
}

// ==================== 核心游戏逻辑 ====================

// 初始化游戏
function initGame() {
  console.log('🎮 星空方块大师 v' + GAME_CONFIG.VERSION + ' - 初始化开始');

  // 初始化音效系统
  AUDIO_MANAGER.init();

  // 初始化网格
  initializeGrid();

  // 加载存档数据
  loadGameData();

  // 初始化方块队列
  refillNextPieces();

  // 绑定触摸事件
  bindTouchEvents();

  // 显示欢迎界面
  showWelcomeScreen();

  // 播放菜单背景音乐
  AUDIO_MANAGER.playMusic('bgm_menu');
  gameState.currentBGM = 'bgm_menu';

  // 开始游戏循环
  gameState.lastUpdateTime = Date.now();
  gameLoop();

  console.log('✅ 游戏初始化完成');
}

// 初始化网格
function initializeGrid() {
  gameState.grid = [];
  for (let y = 0; y < GAME_CONFIG.GRID_HEIGHT; y++) {
    gameState.grid[y] = [];
    for (let x = 0; x < GAME_CONFIG.GRID_WIDTH; x++) {
      gameState.grid[y][x] = null;
    }
  }
}

// 显示欢迎界面
function showWelcomeScreen() {
  gameState.currentScreen = 'menu';
  ctx.fillStyle = '#001122';
  ctx.fillRect(0, 0, windowWidth, windowHeight);
  
  // 标题
  ctx.fillStyle = '#00FFFF';
  ctx.font = `bold ${Math.min(windowWidth / 8, 48)}px Arial`;
  ctx.textAlign = 'center';
  ctx.fillText('🎮 星空方块大师', windowWidth / 2, windowHeight / 2 - 100);
  
  // 副标题
  ctx.fillStyle = '#FFFFFF';
  ctx.font = `${Math.min(windowWidth / 15, 24)}px Arial`;
  ctx.fillText('创新俄罗斯方块体验', windowWidth / 2, windowHeight / 2 - 60);
  
  // 开始提示
  ctx.font = `${Math.min(windowWidth / 18, 20)}px Arial`;
  ctx.fillText('点击屏幕开始游戏', windowWidth / 2, windowHeight / 2 + 20);
  
  // 版本信息
  ctx.font = `${Math.min(windowWidth / 25, 16)}px Arial`;
  ctx.fillStyle = '#888888';
  ctx.fillText(`v${GAME_CONFIG.VERSION} by Brovo865`, windowWidth / 2, windowHeight - 50);
  
  // 游戏模式选择
  ctx.fillStyle = '#CCCCCC';
  ctx.font = `${Math.min(windowWidth / 22, 18)}px Arial`;
  const modes = [
    '🎯 经典模式',
    '🌪️ 重力模式', 
    '💣 炸弹模式',
    '⚡ 竞速模式'
  ];
  
  modes.forEach((mode, index) => {
    ctx.fillText(mode, windowWidth / 2, windowHeight / 2 + 80 + index * 30);
  });
}

// 生成新方块
function spawnNewPiece() {
  if (gameState.nextPieces.length === 0) {
    refillNextPieces();
  }
  
  const pieceType = gameState.nextPieces.shift();
  refillNextPieces();
  
  gameState.currentPiece = {
    type: pieceType,
    shape: JSON.parse(JSON.stringify(TETROMINOS[pieceType].shape)),
    x: Math.floor(GAME_CONFIG.GRID_WIDTH / 2) - Math.floor(TETROMINOS[pieceType].shape[0].length / 2),
    y: 0,
    color: TETROMINOS[pieceType].color,
    specialType: TETROMINOS[pieceType].type,
    rotation: 0
  };
  
  gameState.canHold = true;
  updateGhostPiece();
  
  // 检查游戏结束
  if (!isValidPosition(gameState.currentPiece)) {
    gameOver();
    return false;
  }

  // 检查危险状态（方块接近顶部）
  if (gameState.currentPiece.y < 3) {
    createAudioFeedback('danger', 1.0);
  }

  return true;
}

// 填充下一个方块队列
function refillNextPieces() {
  while (gameState.nextPieces.length < 5) {
    let pieceType;
    const rand = Math.random();
    
    if (gameState.gameMode === 'bomb' && rand < 0.15) {
      const specialTypes = ['BOMB', 'LIGHTNING', 'RAINBOW', 'GOLD'];
      pieceType = specialTypes[Math.floor(Math.random() * specialTypes.length)];
    } else if (rand < 0.05) {
      const specialTypes = ['GOLD', 'RAINBOW'];
      pieceType = specialTypes[Math.floor(Math.random() * specialTypes.length)];
    } else {
      const normalTypes = ['I', 'O', 'T', 'S', 'Z', 'J', 'L'];
      pieceType = normalTypes[Math.floor(Math.random() * normalTypes.length)];
    }
    
    gameState.nextPieces.push(pieceType);
  }
}

// 更新影子方块
function updateGhostPiece() {
  if (!gameState.currentPiece) {
    gameState.ghostPiece = null;
    return;
  }
  
  gameState.ghostPiece = JSON.parse(JSON.stringify(gameState.currentPiece));
  
  while (isValidPosition(gameState.ghostPiece, 0, 1)) {
    gameState.ghostPiece.y++;
  }
}

// 检查位置是否有效
function isValidPosition(piece, dx = 0, dy = 0, newShape = null) {
  if (!piece) return false;
  
  const shape = newShape || piece.shape;
  const newX = piece.x + dx;
  const newY = piece.y + dy;
  
  for (let y = 0; y < shape.length; y++) {
    for (let x = 0; x < shape[y].length; x++) {
      if (shape[y][x]) {
        const gridX = newX + x;
        const gridY = newY + y;
        
        if (gridX < 0 || gridX >= GAME_CONFIG.GRID_WIDTH || gridY >= GAME_CONFIG.GRID_HEIGHT) {
          return false;
        }
        
        if (gridY >= 0 && gameState.grid[gridY][gridX]) {
          return false;
        }
      }
    }
  }
  
  return true;
}

// 旋转方块
function rotatePiece(piece) {
  const shape = piece.shape;
  const size = shape.length;
  const rotated = [];
  
  for (let y = 0; y < size; y++) {
    rotated[y] = [];
    for (let x = 0; x < size; x++) {
      rotated[y][x] = shape[size - 1 - x][y];
    }
  }
  
  return rotated;
}

// 放置方块
function placePiece(piece) {
  for (let y = 0; y < piece.shape.length; y++) {
    for (let x = 0; x < piece.shape[y].length; x++) {
      if (piece.shape[y][x]) {
        const gridY = piece.y + y;
        const gridX = piece.x + x;

        if (gridY >= 0) {
          gameState.grid[gridY][gridX] = {
            color: piece.color,
            type: piece.specialType,
            icon: TETROMINOS[piece.type].icon
          };
        }
      }
    }
  }

  gameState.piecesPlaced++;

  // 播放着陆音效
  AUDIO_MANAGER.playSound('land', 0.8);

  // 处理特殊方块
  if (piece.specialType !== 'normal') {
    handleSpecialBlock(piece);
  }

  // 检查清行
  checkAndClearLines();

  // 增加技能充能
  gameState.skillCharge = Math.min(GAME_CONFIG.MAX_SKILL_CHARGE,
                                  gameState.skillCharge + GAME_CONFIG.SKILL_CHARGE_RATE);
}

// 处理特殊方块
function handleSpecialBlock(piece) {
  const centerX = piece.x + Math.floor(piece.shape[0].length / 2);
  const centerY = piece.y + Math.floor(piece.shape.length / 2);

  switch (piece.specialType) {
    case 'bomb':
      explodeBomb(centerX, centerY);
      AUDIO_MANAGER.playSound('bomb', 1.0);
      break;
    case 'lightning':
      clearLightning(centerX, centerY);
      AUDIO_MANAGER.playSound('lightning', 0.9);
      break;
    case 'rainbow':
      handleRainbowBlock(centerX, centerY);
      AUDIO_MANAGER.playSound('rainbow', 0.8);
      break;
    case 'gold':
      gameState.score += 500;
      createScorePopup(centerX, centerY, '+500');
      AUDIO_MANAGER.playSound('gold', 0.7);
      break;
  }

  gameState.specialBlocksUsed++;
}

// 炸弹爆炸
function explodeBomb(x, y) {
  gameState.screenShake = 10;
  let blocksDestroyed = 0;
  
  // 创建爆炸粒子
  for (let i = 0; i < GAME_CONFIG.EXPLOSION_PARTICLES; i++) {
    gameState.particles.push(new Particle(
      GAME_AREA.x + x * GAME_CONFIG.CELL_SIZE + GAME_CONFIG.CELL_SIZE / 2,
      GAME_AREA.y + y * GAME_CONFIG.CELL_SIZE + GAME_CONFIG.CELL_SIZE / 2,
      '#FF6600',
      'explosion'
    ));
  }
  
  // 清除3x3范围
  for (let dy = -1; dy <= 1; dy++) {
    for (let dx = -1; dx <= 1; dx++) {
      const newX = x + dx;
      const newY = y + dy;
      
      if (newX >= 0 && newX < GAME_CONFIG.GRID_WIDTH && 
          newY >= 0 && newY < GAME_CONFIG.GRID_HEIGHT) {
        if (gameState.grid[newY][newX]) {
          gameState.grid[newY][newX] = null;
          blocksDestroyed++;
        }
      }
    }
  }
  
  const bonus = blocksDestroyed * 50;
  gameState.score += bonus;
  createScorePopup(x, y, `💥 +${bonus}`);
  
  wx.vibrateShort({ type: 'heavy' });
}

// 闪电效果
function clearLightning(x, y) {
  let clearedCount = 0;
  
  if (Math.random() < 0.5) {
    // 清除整行
    for (let gridX = 0; gridX < GAME_CONFIG.GRID_WIDTH; gridX++) {
      if (gameState.grid[y][gridX]) {
        gameState.grid[y][gridX] = null;
        clearedCount++;
      }
    }
  } else {
    // 清除整列
    for (let gridY = 0; gridY < GAME_CONFIG.GRID_HEIGHT; gridY++) {
      if (gameState.grid[gridY][x]) {
        gameState.grid[gridY][x] = null;
        clearedCount++;
      }
    }
  }
  
  const bonus = clearedCount * 30;
  gameState.score += bonus;
  createScorePopup(x, y, `⚡ +${bonus}`);
  
  wx.vibrateShort({ type: 'medium' });
}

// 彩虹方块效果
function handleRainbowBlock(x, y) {
  // 简化版彩虹效果：清除随机颜色的所有方块
  const colors = ['#00FFFF', '#FFFF00', '#800080', '#00FF00', '#FF0000', '#0000FF', '#FFA500'];
  const targetColor = colors[Math.floor(Math.random() * colors.length)];
  
  let clearedCount = 0;
  for (let gridY = 0; gridY < GAME_CONFIG.GRID_HEIGHT; gridY++) {
    for (let gridX = 0; gridX < GAME_CONFIG.GRID_WIDTH; gridX++) {
      if (gameState.grid[gridY][gridX] && gameState.grid[gridY][gridX].color === targetColor) {
        gameState.grid[gridY][gridX] = null;
        clearedCount++;
      }
    }
  }
  
  const bonus = clearedCount * 40;
  gameState.score += bonus;
  createScorePopup(x, y, `🌈 +${bonus}`);
}

// 检查并清除满行
function checkAndClearLines() {
  const linesToClear = [];

  for (let y = GAME_CONFIG.GRID_HEIGHT - 1; y >= 0; y--) {
    let isFull = true;
    for (let x = 0; x < GAME_CONFIG.GRID_WIDTH; x++) {
      if (!gameState.grid[y][x]) {
        isFull = false;
        break;
      }
    }
    if (isFull) {
      linesToClear.push(y);
    }
  }

  if (linesToClear.length > 0) {
    // 播放消除音效
    const clearSounds = ['clear1', 'clear2', 'clear3', 'clear4'];
    const soundIndex = Math.min(linesToClear.length - 1, clearSounds.length - 1);
    AUDIO_MANAGER.playSound(clearSounds[soundIndex], 0.9);

    // 清除满行
    for (const y of linesToClear) {
      clearLine(y);
    }

    // 更新统计
    gameState.lines += linesToClear.length;
    gameState.combo += linesToClear.length;
    gameState.maxCombo = Math.max(gameState.maxCombo, gameState.combo);

    // 连击音效反馈
    createAudioFeedback('combo', gameState.combo / 10);

    // 计算分数
    const baseScore = [0, 100, 300, 500, 800][linesToClear.length] || 1000;
    const totalScore = baseScore * gameState.level + gameState.combo * 50;
    gameState.score += totalScore;

    createScorePopup(GAME_CONFIG.GRID_WIDTH / 2, GAME_CONFIG.GRID_HEIGHT / 2,
                    `${linesToClear.length}行! +${totalScore}`);

    // 升级检查
    if (gameState.lines >= gameState.level * 10) {
      levelUp();
    }

    wx.vibrateShort({ type: 'light' });
  } else {
    gameState.combo = 0;
  }
}

// 清除单行
function clearLine(y) {
  // 创建粒子效果
  for (let x = 0; x < GAME_CONFIG.GRID_WIDTH; x++) {
    if (gameState.grid[y][x]) {
      for (let i = 0; i < 3; i++) {
        gameState.particles.push(new Particle(
          GAME_AREA.x + x * GAME_CONFIG.CELL_SIZE + GAME_CONFIG.CELL_SIZE / 2,
          GAME_AREA.y + y * GAME_CONFIG.CELL_SIZE + GAME_CONFIG.CELL_SIZE / 2,
          gameState.grid[y][x].color
        ));
      }
    }
  }
  
  // 移除行
  gameState.grid.splice(y, 1);
  gameState.grid.unshift(new Array(GAME_CONFIG.GRID_WIDTH).fill(null));
}

// 升级
function levelUp() {
  gameState.level++;
  createLevelUpEffect();
  wx.vibrateShort({ type: 'heavy' });

  // 播放升级音效
  AUDIO_MANAGER.playSound('levelup', 1.0);

  // 根据等级切换背景音乐
  if (gameState.level >= 10 && gameState.currentBGM !== 'bgm_intense') {
    AUDIO_MANAGER.playMusic('bgm_intense');
    gameState.currentBGM = 'bgm_intense';
  }

  const levelBonus = gameState.level * 200;
  gameState.score += levelBonus;
  createScorePopup(GAME_CONFIG.GRID_WIDTH / 2, 5, `升级! +${levelBonus}`);
}

// 获取当前速度
function getCurrentSpeed() {
  let speed = GAME_CONFIG.INITIAL_SPEED;
  for (let i = 1; i < gameState.level; i++) {
    speed *= GAME_CONFIG.SPEED_MULTIPLIER;
  }
  return Math.max(GAME_CONFIG.MIN_SPEED, speed);
}

// 动态音乐系统
function updateDynamicMusic() {
  // 根据游戏状态动态切换音乐
  let targetBGM = 'bgm_game';

  // 高等级使用紧张音乐
  if (gameState.level >= 10) {
    targetBGM = 'bgm_intense';
  }

  // 竞速模式最后30秒使用紧张音乐
  if (gameState.gameMode === 'timeattack' && gameState.timeRemaining < 30000) {
    targetBGM = 'bgm_intense';
  }

  // 网格接近顶部时使用紧张音乐
  let topFilledRow = GAME_CONFIG.GRID_HEIGHT;
  for (let y = 0; y < GAME_CONFIG.GRID_HEIGHT; y++) {
    for (let x = 0; x < GAME_CONFIG.GRID_WIDTH; x++) {
      if (gameState.grid[y][x]) {
        topFilledRow = y;
        break;
      }
    }
    if (topFilledRow < GAME_CONFIG.GRID_HEIGHT) break;
  }

  if (topFilledRow < 5) {
    targetBGM = 'bgm_intense';
  }

  // 切换音乐
  if (targetBGM !== gameState.currentBGM) {
    AUDIO_MANAGER.playMusic(targetBGM);
    gameState.currentBGM = targetBGM;
  }
}

// 创建音效反馈
function createAudioFeedback(type, intensity = 1.0) {
  switch (type) {
    case 'combo':
      if (gameState.combo >= 5) {
        AUDIO_MANAGER.playSound('rainbow', 0.6 * intensity);
      }
      break;
    case 'danger':
      // 当方块接近顶部时的警告音效
      if (Math.random() < 0.3) {
        AUDIO_MANAGER.playSound('lightning', 0.3 * intensity);
      }
      break;
  }
}

// ==================== 特效系统 ====================

// 创建粒子
function createParticles(x, y, color, type, count) {
  for (let i = 0; i < count; i++) {
    gameState.particles.push(new Particle(x, y, color, type));
  }
}

// 创建分数弹出
function createScorePopup(x, y, text) {
  gameState.animations.push({
    type: 'scorePopup',
    x: GAME_AREA.x + x * GAME_CONFIG.CELL_SIZE,
    y: GAME_AREA.y + y * GAME_CONFIG.CELL_SIZE,
    text: text,
    life: 1.0,
    startTime: Date.now()
  });
}

// 创建升级特效
function createLevelUpEffect() {
  for (let i = 0; i < 30; i++) {
    gameState.particles.push(new Particle(
      windowWidth / 2,
      windowHeight / 2,
      '#FFD700',
      'explosion'
    ));
  }
}

// ==================== 输入处理 ====================

// 绑定触摸事件
function bindTouchEvents() {
  let touchStartX = 0;
  let touchStartY = 0;
  let touchStartTime = 0;
  
  wx.onTouchStart((e) => {
    touchStartX = e.touches[0].clientX;
    touchStartY = e.touches[0].clientY;
    touchStartTime = Date.now();
  });
  
  wx.onTouchEnd((e) => {
    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    const touchDuration = Date.now() - touchStartTime;
    
    const deltaX = touchEndX - touchStartX;
    const deltaY = touchEndY - touchStartY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    // 菜单界面处理
    if (gameState.currentScreen === 'menu') {
      // 简单的模式选择
      if (touchEndY < windowHeight / 2 + 60) {
        startGame('classic');
      } else if (touchEndY < windowHeight / 2 + 90) {
        startGame('gravity');
      } else if (touchEndY < windowHeight / 2 + 120) {
        startGame('bomb');
      } else if (touchEndY < windowHeight / 2 + 150) {
        startGame('timeattack');
      } else {
        startGame('classic');
      }
      return;
    }
    
    // 游戏中处理
    if (!gameState.isRunning) return;
    
    if (gameState.isPaused) {
      resumeGame();
      return;
    }
    
    // 短按 - 旋转
    if (touchDuration < 200 && distance < 30) {
      if (gameState.currentPiece) {
        rotatePieceIfPossible();
      }
      return;
    }
    
    // 长按 - 暂停
    if (touchDuration > 500 && distance < 30) {
      pauseGame();
      return;
    }
    
    // 检查是否点击音效按钮
    const audioButtonSize = Math.min(windowWidth / 20, 30);
    const audioButtonX = windowWidth - audioButtonSize - 10;
    const audioButtonY = windowHeight - audioButtonSize - 10;

    if (touchEndX >= audioButtonX && touchEndX <= audioButtonX + audioButtonSize &&
        touchEndY >= audioButtonY && touchEndY <= audioButtonY + audioButtonSize) {
      const audioEnabled = AUDIO_MANAGER.toggleAudio();
      showToast(audioEnabled ? '音效已开启' : '音效已关闭');
      return;
    }

    // 滑动手势
    if (distance > 30) {
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        // 水平滑动
        if (deltaX > 0) {
          movePiece(1, 0);
        } else {
          movePiece(-1, 0);
        }
      } else {
        // 垂直滑动
        if (deltaY > 0) {
          softDrop();
        } else {
          holdPiece();
        }
      }
    }
  });
}

// 移动方块
function movePiece(dx, dy) {
  if (!gameState.currentPiece) return false;

  if (isValidPosition(gameState.currentPiece, dx, dy)) {
    gameState.currentPiece.x += dx;
    gameState.currentPiece.y += dy;
    updateGhostPiece();

    // 播放移动音效
    if (dx !== 0) {
      AUDIO_MANAGER.playSound('move', 0.6);
    }

    return true;
  }
  return false;
}

// 旋转方块
function rotatePieceIfPossible() {
  if (!gameState.currentPiece) return false;

  const rotated = rotatePiece(gameState.currentPiece);

  if (isValidPosition(gameState.currentPiece, 0, 0, rotated)) {
    gameState.currentPiece.shape = rotated;
    updateGhostPiece();
    AUDIO_MANAGER.playSound('rotate', 0.7);
    return true;
  }

  // 尝试墙踢
  const kicks = [[-1, 0], [1, 0], [0, -1]];
  for (const [dx, dy] of kicks) {
    if (isValidPosition(gameState.currentPiece, dx, dy, rotated)) {
      gameState.currentPiece.shape = rotated;
      gameState.currentPiece.x += dx;
      gameState.currentPiece.y += dy;
      updateGhostPiece();
      AUDIO_MANAGER.playSound('rotate', 0.7);
      return true;
    }
  }

  return false;
}

// 软降
function softDrop() {
  if (movePiece(0, 1)) {
    gameState.score += 1;
    AUDIO_MANAGER.playSound('drop', 0.4);
  }
}

// 暂存方块
function holdPiece() {
  if (!gameState.currentPiece || !gameState.canHold) return;
  
  const heldType = gameState.holdPiece;
  gameState.holdPiece = gameState.currentPiece.type;
  gameState.canHold = false;
  
  if (heldType) {
    gameState.currentPiece = {
      type: heldType,
      shape: JSON.parse(JSON.stringify(TETROMINOS[heldType].shape)),
      x: Math.floor(GAME_CONFIG.GRID_WIDTH / 2) - Math.floor(TETROMINOS[heldType].shape[0].length / 2),
      y: 0,
      color: TETROMINOS[heldType].color,
      specialType: TETROMINOS[heldType].type,
      rotation: 0
    };
    updateGhostPiece();
  } else {
    spawnNewPiece();
  }
}

// ==================== 游戏流程控制 ====================

// 开始游戏
function startGame(mode = 'classic') {
  console.log(`🎮 开始游戏 - 模式: ${mode}`);

  resetGameState();
  gameState.gameMode = mode;
  gameState.isRunning = true;
  gameState.isPaused = false;
  gameState.currentScreen = 'game';
  gameState.lastUpdateTime = Date.now();

  // 切换到游戏背景音乐
  AUDIO_MANAGER.playMusic('bgm_game');
  gameState.currentBGM = 'bgm_game';

  spawnNewPiece();

  showToast(`开始 ${getModeName(mode)} 模式！`);
}

// 暂停游戏
function pauseGame() {
  if (!gameState.isRunning) return;
  gameState.isPaused = true;
  AUDIO_MANAGER.pauseMusic();
  AUDIO_MANAGER.playSound('pause', 0.8);
  showToast('游戏暂停');
}

// 恢复游戏
function resumeGame() {
  if (!gameState.isRunning || !gameState.isPaused) return;
  gameState.isPaused = false;
  gameState.lastUpdateTime = Date.now();
  AUDIO_MANAGER.resumeMusic();
  AUDIO_MANAGER.playSound('resume', 0.8);
  showToast('游戏继续');
}

// 游戏结束
function gameOver() {
  console.log('🎮 游戏结束');
  gameState.isRunning = false;

  // 停止背景音乐并播放游戏结束音效
  AUDIO_MANAGER.stopMusic();
  AUDIO_MANAGER.playSound('gameover', 1.0);

  saveGameData();

  setTimeout(() => {
    showGameOverDialog();
  }, 1000);
}

// 重置游戏状态
function resetGameState() {
  initializeGrid();
  
  gameState.score = 0;
  gameState.level = 1;
  gameState.lines = 0;
  gameState.combo = 0;
  gameState.gameTime = 0;
  gameState.fallTime = 0;
  gameState.piecesPlaced = 0;
  gameState.specialBlocksUsed = 0;
  gameState.skillCharge = 0;
  gameState.activeSkills = [];
  gameState.skillCooldowns = {};
  gameState.particles = [];
  gameState.animations = [];
  gameState.currentPiece = null;
  gameState.nextPieces = [];
  gameState.holdPiece = null;
  gameState.canHold = true;
  gameState.ghostPiece = null;
  
  refillNextPieces();
}

// 获取模式名称
function getModeName(mode) {
  const names = {
    classic: '经典',
    gravity: '重力',
    bomb: '炸弹',
    timeattack: '竞速',
    coop: '合作'
  };
  return names[mode] || '未知';
}

// ==================== UI显示 ====================

// 显示游戏结束对话框
function showGameOverDialog() {
  const isNewRecord = gameState.score > gameState.highScore;

  wx.showModal({
    title: isNewRecord ? '🎉 新纪录！' : '🎮 游戏结束',
    content: `得分: ${gameState.score}\n最高分: ${Math.max(gameState.score, gameState.highScore)}\n等级: ${gameState.level}\n消除: ${gameState.lines}行`,
    confirmText: '再来一局',
    cancelText: '返回菜单',
    success: (res) => {
      if (res.confirm) {
        startGame(gameState.gameMode);
      } else {
        gameState.currentScreen = 'menu';
        AUDIO_MANAGER.playMusic('bgm_menu');
        gameState.currentBGM = 'bgm_menu';
        showWelcomeScreen();
      }
    }
  });
}

// 显示提示消息
function showToast(message) {
  gameState.animations.push({
    type: 'toast',
    x: windowWidth / 2,
    y: 100,
    text: message,
    life: 1.0,
    startTime: Date.now()
  });
}

// ==================== 数据存储 ====================

// 加载游戏数据
function loadGameData() {
  try {
    const highScore = wx.getStorageSync('tetris_high_score_v2');
    if (highScore) {
      gameState.highScore = highScore;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
  }
}

// 保存游戏数据
function saveGameData() {
  try {
    if (gameState.score > gameState.highScore) {
      gameState.highScore = gameState.score;
      wx.setStorageSync('tetris_high_score_v2', gameState.highScore);
    }
  } catch (error) {
    console.error('保存数据失败:', error);
  }
}

// ==================== 主游戏循环 ====================

// 游戏主循环
function gameLoop() {
  const currentTime = Date.now();
  const deltaTime = currentTime - gameState.lastUpdateTime;
  gameState.lastUpdateTime = currentTime;
  
  update(deltaTime);
  render();
  
  requestAnimationFrame(gameLoop);
}

// 更新逻辑
function update(deltaTime) {
  if (gameState.currentScreen !== 'game' || !gameState.isRunning || gameState.isPaused) {
    return;
  }
  
  gameState.gameTime += deltaTime;
  gameState.frameCount++;
  
  // 更新粒子
  gameState.particles = gameState.particles.filter(particle => {
    particle.update();
    return !particle.isDead();
  });
  
  // 更新动画
  gameState.animations = gameState.animations.filter(animation => {
    animation.life -= 0.02;
    return animation.life > 0;
  });
  
  // 更新屏幕震动
  if (gameState.screenShake > 0) {
    gameState.screenShake--;
  }
  
  // 更新闪光效果
  if (gameState.flashEffect > 0) {
    gameState.flashEffect -= 0.05;
  }
  
  // 方块下落
  if (gameState.currentPiece) {
    gameState.fallTime += deltaTime;
    const fallSpeed = getCurrentSpeed();

    if (gameState.fallTime >= fallSpeed) {
      if (!movePiece(0, 1)) {
        placePiece(gameState.currentPiece);
        spawnNewPiece();
      }
      gameState.fallTime = 0;
    }
  }

  // 动态音乐切换
  updateDynamicMusic();
  
  // 竞速模式时间更新
  if (gameState.gameMode === 'timeattack') {
    gameState.timeRemaining -= deltaTime;
    if (gameState.timeRemaining <= 0) {
      gameState.timeRemaining = 0;
      gameOver();
    }
  }
}

// 渲染
function render() {
  // 清空画布
  ctx.fillStyle = '#001122';
  ctx.fillRect(0, 0, windowWidth, windowHeight);
  
  if (gameState.currentScreen === 'menu') {
    showWelcomeScreen();
    return;
  }
  
  if (gameState.currentScreen !== 'game') return;
  
  // 应用屏幕震动
  if (gameState.screenShake > 0) {
    const shakeX = (Math.random() - 0.5) * gameState.screenShake;
    const shakeY = (Math.random() - 0.5) * gameState.screenShake;
    ctx.save();
    ctx.translate(shakeX, shakeY);
  }
  
  // 绘制游戏区域边框
  ctx.strokeStyle = '#FFFFFF';
  ctx.lineWidth = 2;
  ctx.strokeRect(GAME_AREA.x - 2, GAME_AREA.y - 2, GAME_AREA.width + 4, GAME_AREA.height + 4);
  
  // 绘制网格
  drawGrid();
  
  // 绘制已放置的方块
  drawPlacedBlocks();
  
  // 绘制影子方块
  if (gameState.ghostPiece) {
    drawGhostPiece();
  }
  
  // 绘制当前方块
  if (gameState.currentPiece) {
    drawPiece(gameState.currentPiece);
  }
  
  // 绘制粒子
  gameState.particles.forEach(particle => particle.draw());
  
  // 绘制UI
  drawUI();
  
  // 绘制动画
  drawAnimations();
  
  // 闪光效果
  if (gameState.flashEffect > 0) {
    ctx.fillStyle = `rgba(255, 255, 255, ${gameState.flashEffect * 0.3})`;
    ctx.fillRect(0, 0, windowWidth, windowHeight);
  }
  
  // 暂停界面
  if (gameState.isPaused) {
    drawPauseScreen();
  }
  
  if (gameState.screenShake > 0) {
    ctx.restore();
  }
}

// 绘制网格
function drawGrid() {
  ctx.strokeStyle = '#003366';
  ctx.lineWidth = 0.5;
  
  for (let x = 0; x <= GAME_CONFIG.GRID_WIDTH; x++) {
    const posX = GAME_AREA.x + x * GAME_CONFIG.CELL_SIZE;
    ctx.beginPath();
    ctx.moveTo(posX, GAME_AREA.y);
    ctx.lineTo(posX, GAME_AREA.y + GAME_AREA.height);
    ctx.stroke();
  }
  
  for (let y = 0; y <= GAME_CONFIG.GRID_HEIGHT; y++) {
    const posY = GAME_AREA.y + y * GAME_CONFIG.CELL_SIZE;
    ctx.beginPath();
    ctx.moveTo(GAME_AREA.x, posY);
    ctx.lineTo(GAME_AREA.x + GAME_AREA.width, posY);
    ctx.stroke();
  }
}

// 绘制已放置的方块
function drawPlacedBlocks() {
  for (let y = 0; y < GAME_CONFIG.GRID_HEIGHT; y++) {
    for (let x = 0; x < GAME_CONFIG.GRID_WIDTH; x++) {
      const cell = gameState.grid[y][x];
      if (cell) {
        const posX = GAME_AREA.x + x * GAME_CONFIG.CELL_SIZE;
        const posY = GAME_AREA.y + y * GAME_CONFIG.CELL_SIZE;
        
        ctx.fillStyle = cell.color;
        ctx.fillRect(posX + 1, posY + 1, GAME_CONFIG.CELL_SIZE - 2, GAME_CONFIG.CELL_SIZE - 2);
        
        // 特殊方块图标
        if (cell.icon) {
          ctx.font = `${GAME_CONFIG.CELL_SIZE * 0.6}px Arial`;
          ctx.textAlign = 'center';
          ctx.fillStyle = '#FFFFFF';
          ctx.fillText(cell.icon, posX + GAME_CONFIG.CELL_SIZE / 2, posY + GAME_CONFIG.CELL_SIZE * 0.7);
        }
      }
    }
  }
}

// 绘制影子方块
function drawGhostPiece() {
  const piece = gameState.ghostPiece;
  if (!piece) return;
  
  ctx.save();
  ctx.globalAlpha = 0.3;
  
  for (let y = 0; y < piece.shape.length; y++) {
    for (let x = 0; x < piece.shape[y].length; x++) {
      if (piece.shape[y][x]) {
        const posX = GAME_AREA.x + (piece.x + x) * GAME_CONFIG.CELL_SIZE;
        const posY = GAME_AREA.y + (piece.y + y) * GAME_CONFIG.CELL_SIZE;
        
        ctx.fillStyle = piece.color;
        ctx.fillRect(posX + 1, posY + 1, GAME_CONFIG.CELL_SIZE - 2, GAME_CONFIG.CELL_SIZE - 2);
      }
    }
  }
  
  ctx.restore();
}

// 绘制方块
function drawPiece(piece) {
  if (!piece) return;
  
  for (let y = 0; y < piece.shape.length; y++) {
    for (let x = 0; x < piece.shape[y].length; x++) {
      if (piece.shape[y][x]) {
        const posX = GAME_AREA.x + (piece.x + x) * GAME_CONFIG.CELL_SIZE;
        const posY = GAME_AREA.y + (piece.y + y) * GAME_CONFIG.CELL_SIZE;
        
        // 彩虹色效果
        if (piece.color === '#FF69B4') {
          const hue = (Date.now() / 20) % 360;
          ctx.fillStyle = `hsl(${hue}, 80%, 60%)`;
        } else {
          ctx.fillStyle = piece.color;
        }
        
        ctx.fillRect(posX + 1, posY + 1, GAME_CONFIG.CELL_SIZE - 2, GAME_CONFIG.CELL_SIZE - 2);
        
        // 特殊方块图标
        if (TETROMINOS[piece.type].icon) {
          ctx.font = `${GAME_CONFIG.CELL_SIZE * 0.6}px Arial`;
          ctx.textAlign = 'center';
          ctx.fillStyle = '#FFFFFF';
          ctx.fillText(TETROMINOS[piece.type].icon, posX + GAME_CONFIG.CELL_SIZE / 2, posY + GAME_CONFIG.CELL_SIZE * 0.7);
        }
      }
    }
  }
}

// 绘制UI
function drawUI() {
  // 分数
  ctx.fillStyle = '#FFFFFF';
  ctx.font = `bold ${Math.min(windowWidth / 15, 24)}px Arial`;
  ctx.textAlign = 'left';
  ctx.fillText(`分数: ${gameState.score}`, 20, 40);
  
  ctx.font = `${Math.min(windowWidth / 20, 18)}px Arial`;
  ctx.fillText(`等级: ${gameState.level}`, 20, 70);
  ctx.fillText(`行数: ${gameState.lines}`, 20, 100);
  
  if (gameState.combo > 0) {
    ctx.fillStyle = '#FFD700';
    ctx.fillText(`连击: ${gameState.combo}`, 20, 130);
  }
  
  // 最高分
  ctx.fillStyle = '#CCCCCC';
  ctx.textAlign = 'right';
  ctx.fillText(`最高分: ${gameState.highScore}`, windowWidth - 20, 40);
  
  // 技能充能
  const skillBarWidth = 100;
  const skillBarHeight = 8;
  const skillBarX = windowWidth - skillBarWidth - 20;
  const skillBarY = 60;
  
  ctx.fillStyle = '#333333';
  ctx.fillRect(skillBarX, skillBarY, skillBarWidth, skillBarHeight);
  
  ctx.fillStyle = '#00FFFF';
  ctx.fillRect(skillBarX, skillBarY, (gameState.skillCharge / GAME_CONFIG.MAX_SKILL_CHARGE) * skillBarWidth, skillBarHeight);
  
  ctx.fillStyle = '#FFFFFF';
  ctx.font = `${Math.min(windowWidth / 25, 14)}px Arial`;
  ctx.textAlign = 'right';
  ctx.fillText('技能', windowWidth - 20, skillBarY - 5);
  
  // 下一个方块预览
  if (gameState.nextPieces.length > 0) {
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `${Math.min(windowWidth / 25, 14)}px Arial`;
    ctx.textAlign = 'left';
    ctx.fillText('下一个:', 20, windowHeight - 120);
    
    const nextPiece = TETROMINOS[gameState.nextPieces[0]];
    const previewSize = GAME_CONFIG.CELL_SIZE * 0.5;
    
    for (let y = 0; y < nextPiece.shape.length; y++) {
      for (let x = 0; x < nextPiece.shape[y].length; x++) {
        if (nextPiece.shape[y][x]) {
          ctx.fillStyle = nextPiece.color;
          ctx.fillRect(
            20 + x * previewSize,
            windowHeight - 100 + y * previewSize,
            previewSize - 1,
            previewSize - 1
          );
        }
      }
    }
  }
  
  // 暂存方块
  if (gameState.holdPiece) {
    ctx.fillStyle = '#FFFFFF';
    ctx.font = `${Math.min(windowWidth / 25, 14)}px Arial`;
    ctx.textAlign = 'right';
    ctx.fillText('暂存:', windowWidth - 20, windowHeight - 120);
    
    const holdPiece = TETROMINOS[gameState.holdPiece];
    const previewSize = GAME_CONFIG.CELL_SIZE * 0.5;
    
    for (let y = 0; y < holdPiece.shape.length; y++) {
      for (let x = 0; x < holdPiece.shape[y].length; x++) {
        if (holdPiece.shape[y][x]) {
          ctx.fillStyle = gameState.canHold ? holdPiece.color : '#666666';
          ctx.fillRect(
            windowWidth - 20 - (holdPiece.shape[0].length - x) * previewSize,
            windowHeight - 100 + y * previewSize,
            previewSize - 1,
            previewSize - 1
          );
        }
      }
    }
  }
  
  // 竞速模式时间
  if (gameState.gameMode === 'timeattack') {
    const minutes = Math.floor(gameState.timeRemaining / 60000);
    const seconds = Math.floor((gameState.timeRemaining % 60000) / 1000);

    ctx.fillStyle = gameState.timeRemaining < 30000 ? '#FF0000' : '#FFFFFF';
    ctx.font = `bold ${Math.min(windowWidth / 12, 28)}px Arial`;
    ctx.textAlign = 'center';
    ctx.fillText(`${minutes}:${seconds.toString().padStart(2, '0')}`, windowWidth / 2, 40);
  }

  // 音效控制按钮
  const audioButtonSize = Math.min(windowWidth / 20, 30);
  const audioButtonX = windowWidth - audioButtonSize - 10;
  const audioButtonY = windowHeight - audioButtonSize - 10;

  ctx.fillStyle = GAME_CONFIG.AUDIO_ENABLED ? '#00FF00' : '#FF0000';
  ctx.fillRect(audioButtonX, audioButtonY, audioButtonSize, audioButtonSize);

  ctx.fillStyle = '#FFFFFF';
  ctx.font = `${audioButtonSize * 0.6}px Arial`;
  ctx.textAlign = 'center';
  ctx.fillText(GAME_CONFIG.AUDIO_ENABLED ? '🔊' : '🔇',
               audioButtonX + audioButtonSize / 2,
               audioButtonY + audioButtonSize * 0.7);
}

// 绘制动画
function drawAnimations() {
  gameState.animations.forEach(animation => {
    ctx.save();
    ctx.globalAlpha = animation.life;
    
    switch (animation.type) {
      case 'scorePopup':
        ctx.fillStyle = '#FFD700';
        ctx.font = `bold ${Math.min(windowWidth / 20, 20)}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(animation.text, animation.x, animation.y - (1 - animation.life) * 50);
        break;
        
      case 'toast':
        ctx.fillStyle = '#FFFFFF';
        ctx.font = `${Math.min(windowWidth / 18, 18)}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(animation.text, animation.x, animation.y);
        break;
    }
    
    ctx.restore();
  });
}

// 绘制暂停界面
function drawPauseScreen() {
  ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  ctx.fillRect(0, 0, windowWidth, windowHeight);
  
  ctx.fillStyle = '#FFFFFF';
  ctx.font = `bold ${Math.min(windowWidth / 10, 32)}px Arial`;
  ctx.textAlign = 'center';
  ctx.fillText('游戏暂停', windowWidth / 2, windowHeight / 2);
  
  ctx.font = `${Math.min(windowWidth / 15, 20)}px Arial`;
  ctx.fillText('点击继续', windowWidth / 2, windowHeight / 2 + 50);
}

// ==================== 启动游戏 ====================

// 初始化并启动游戏
initGame();

console.log('🎮 星空方块大师启动完成!');