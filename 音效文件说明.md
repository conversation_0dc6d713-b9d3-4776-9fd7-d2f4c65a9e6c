# 🎵 星空方块大师 - 音效文件说明

## 📁 文件结构
```
sounds/
├── move.mp3          # 方块移动音效
├── rotate.mp3        # 方块旋转音效
├── drop.mp3          # 软降音效
├── land.mp3          # 方块着陆音效
├── clear1.mp3        # 消除1行音效
├── clear2.mp3        # 消除2行音效
├── clear3.mp3        # 消除3行音效
├── clear4.mp3        # 消除4行音效（四消）
├── bomb.mp3          # 炸弹爆炸音效
├── lightning.mp3     # 闪电音效
├── rainbow.mp3       # 彩虹方块音效
├── gold.mp3          # 金块音效
├── levelup.mp3       # 升级音效
├── gameover.mp3      # 游戏结束音效
├── pause.mp3         # 暂停音效
└── resume.mp3        # 恢复音效

music/
├── menu.mp3          # 菜单背景音乐
├── game.mp3          # 游戏背景音乐
└── intense.mp3       # 紧张背景音乐
```

## 🎼 音效特性说明

### 基础操作音效
- **move.mp3**: 短促的"滴"声，频率约200-300Hz
- **rotate.mp3**: 轻快的"咔"声，频率约400-500Hz
- **drop.mp3**: 快速的"嗖"声，音调下降
- **land.mp3**: 沉闷的"咚"声，频率约100-200Hz

### 消除音效
- **clear1.mp3**: 清脆的"叮"声
- **clear2.mp3**: 双重"叮叮"声
- **clear3.mp3**: 三重"叮叮叮"声，音调递增
- **clear4.mp3**: 华丽的和弦音效，庆祝感强

### 特殊方块音效
- **bomb.mp3**: 爆炸声"轰"，低频重音
- **lightning.mp3**: 电击声"滋滋"，高频尖锐
- **rainbow.mp3**: 魔法音效，音调变化丰富
- **gold.mp3**: 金币声"叮铃"，清脆悦耳

### 游戏状态音效
- **levelup.mp3**: 胜利号角，激励感强
- **gameover.mp3**: 失败音效，低沉悲伤
- **pause.mp3**: 暂停提示音
- **resume.mp3**: 恢复提示音

### 背景音乐
- **menu.mp3**: 轻松愉快的循环音乐，时长30-60秒
- **game.mp3**: 节奏感强的游戏音乐，时长60-120秒
- **intense.mp3**: 紧张刺激的音乐，适合高难度场景

## 🔧 技术要求

### 音频格式
- **格式**: MP3 (推荐) 或 AAC
- **采样率**: 44.1kHz 或 22.05kHz
- **比特率**: 128kbps (音效) / 192kbps (音乐)
- **声道**: 单声道 (音效) / 立体声 (音乐)

### 文件大小
- **音效文件**: 建议 < 50KB
- **音乐文件**: 建议 < 2MB
- **总大小**: 建议整个音频包 < 10MB

### 音量平衡
- **音效**: 峰值 -6dB，避免削波
- **音乐**: 峰值 -3dB，保持动态范围
- **统一**: 所有文件音量保持一致

## 🎨 创作建议

### 音效设计
1. **简洁明快**: 音效时长控制在0.1-0.5秒
2. **频率分离**: 不同音效使用不同频段，避免冲突
3. **情感表达**: 正面操作用明亮音色，负面用低沉音色
4. **循环友好**: 背景音乐设计无缝循环点

### 风格统一
- **主题**: 科幻/太空/星空风格
- **音色**: 电子合成器为主，适当加入管弦乐
- **节奏**: 游戏音乐保持4/4拍，BPM 120-140

## 📱 微信小游戏适配

### API兼容
- 使用 `wx.createInnerAudioContext()` 播放
- 支持预加载和音量控制
- 兼容iOS和Android平台

### 性能优化
- 音效文件预加载到内存
- 避免同时播放过多音效
- 实现音效冷却机制防止重叠

### 用户体验
- 提供音效开关控制
- 支持音量调节
- 后台播放时自动暂停

## 🚀 实现状态

✅ 音效系统框架已完成
✅ 动态音乐切换已实现
✅ 音效控制UI已添加
⏳ 需要准备实际音频文件
⏳ 可选：添加更多音效变化

## 📝 使用说明

1. 将音频文件放置在对应目录
2. 确保文件名与代码中定义一致
3. 在微信开发者工具中测试音效
4. 根据实际效果调整音量和时机

---
*如果暂时没有音频文件，游戏会自动创建静音占位符，不影响正常运行*
